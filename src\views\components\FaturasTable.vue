<template>
  <div class="faturas-container">
    <!-- Filtros -->
    <div class="row mb-4">
      <div class="col-md-3">
        <label class="form-label">Paciente</label>
        <select v-model="filtros.paciente_id" @change="aplicarFiltros" class="form-select">
          <option value="">Todos os pacientes</option>
          <option v-for="paciente in pacientes" :key="paciente.id" :value="paciente.id">
            {{ paciente.nome }}
          </option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Status</label>
        <select v-model="filtros.status" @change="aplicarFiltros" class="form-select">
          <option value="">Todos</option>
          <option value="pendente">Pendente</option>
          <option value="pago">Pago</option>
          <option value="vencido">Vencido</option>
          <option value="cancelado">Cancelado</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Data Início</label>
        <input v-model="filtros.data_inicio" @change="aplicarFiltros" type="date" class="form-control">
      </div>
      <div class="col-md-2">
        <label class="form-label">Data Fim</label>
        <input v-model="filtros.data_fim" @change="aplicarFiltros" type="date" class="form-control">
      </div>
      <div class="col-md-2">
        <label class="form-label">Apenas Vencidas</label>
        <div class="form-check mt-2">
          <input v-model="filtros.vencidas" @change="aplicarFiltros" type="checkbox" class="form-check-input" id="vencidas">
          <label class="form-check-label" for="vencidas">Sim</label>
        </div>
      </div>
      <div class="col-md-1 d-flex align-items-end">
        <button @click="limparFiltros" class="btn btn-outline-secondary btn-sm">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="isLoading" class="w-100 text-center py-3">
      <div class="spinner-border text-primary" role="status"></div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!faturas || faturas.length === 0" class="empty-state">
      <div class="empty-state-message">
        <div class="icon-wrapper">
          <font-awesome-icon :icon="['fas', 'file-invoice-dollar']" class="empty-state-icon" />
        </div>
        <p>Não há faturas registradas.</p>
      </div>
    </div>

    <!-- Tabela de Faturas -->
    <div v-else>
      <EasyDataTable
        :headers="headers"
        :items="faturas"
        :rows-per-page="10"
        :rows-items="[10, 25, 50]"
        body-row-class-name="fatura-row"
        header-item-class-name="table-header-item"
        body-item-class-name="table-body-item"
        rows-per-page-message="Faturas por página"
        rows-of-page-separator-message="de"
        empty-message="Sem faturas registradas"
      >
        <template #header-paciente>
          <div class="w-100">PACIENTE</div>
        </template>

        <template #header-descricao>
          <div class="w-100">DESCRIÇÃO</div>
        </template>

        <template #header-valor_final>
          <div class="text-center w-100">VALOR</div>
        </template>

        <template #header-data_vencimento>
          <div class="text-center w-100">VENCIMENTO</div>
        </template>

        <template #header-status>
          <div class="text-center w-100">STATUS</div>
        </template>

        <template #header-parcelas>
          <div class="text-center w-100">PARCELAS</div>
        </template>

        <template #header-acoes>
          <div class="text-center w-100">AÇÕES</div>
        </template>

        <template #item-paciente="{ paciente }">
          <div class="d-flex flex-column justify-content-center">
            <p class="text-xs font-weight-bold mb-0">
              {{ paciente?.nome || 'N/A' }}
            </p>
          </div>
        </template>

        <template #item-descricao="{ descricao }">
          <div class="d-flex flex-column justify-content-center">
            <p class="text-xs mb-0" :title="descricao">
              {{ truncateText(descricao, 30) }}
            </p>
          </div>
        </template>

        <template #item-valor_final="{ valor_final }">
          <div class="d-flex justify-content-center">
            <span class="valor-badge">
              {{ formatarValor(valor_final) }}
            </span>
          </div>
        </template>

        <template #item-data_vencimento="{ data_vencimento }">
          <div class="d-flex justify-content-center">
            <span class="text-xs">
              {{ formatarData(data_vencimento) }}
            </span>
          </div>
        </template>

        <template #item-status="{ status }">
          <div class="d-flex justify-content-center">
            <span class="status-badge" :class="getStatusClass(status)">
              {{ getStatusText(status) }}
            </span>
          </div>
        </template>

        <template #item-parcelas="{ parcela_numero, parcelas_total }">
          <div class="d-flex justify-content-center">
            <span class="text-xs" v-if="parcelas_total > 1">
              {{ parcela_numero }}/{{ parcelas_total }}
            </span>
            <span class="text-xs" v-else>
              À vista
            </span>
          </div>
        </template>

        <template #item-acoes="item">
          <div class="d-flex justify-content-center gap-1">
            <button 
              @click="editarFatura(item)" 
              class="btn btn-sm btn-outline-primary"
              title="Editar"
            >
              <font-awesome-icon :icon="['fas', 'edit']" />
            </button>
            
            <button 
              v-if="item.status === 'pendente' || item.status === 'vencido'"
              @click="marcarComoPago(item)" 
              class="btn btn-sm btn-outline-success"
              title="Marcar como Pago"
            >
              <font-awesome-icon :icon="['fas', 'check']" />
            </button>
            
            <button 
              @click="excluirFatura(item)" 
              class="btn btn-sm btn-outline-danger"
              title="Excluir"
            >
              <font-awesome-icon :icon="['fas', 'trash']" />
            </button>
          </div>
        </template>
      </EasyDataTable>
    </div>
  </div>
</template>

<script>
import { getFaturas, excluirFatura, marcarComoPago, formatarValor, formatarData, getStatusClass, getStatusText } from '@/services/faturaService';
import { getListaSimplesPacientes } from '@/services/pacientesService';
import cSwal from '@/utils/cSwal.js';
import EasyDataTable from 'vue3-easy-data-table';
import 'vue3-easy-data-table/dist/style.css';

export default {
  name: 'FaturasTable',
  components: {
    EasyDataTable
  },
  data() {
    return {
      faturas: [],
      pacientes: [],
      isLoading: false,
      filtros: {
        paciente_id: '',
        status: '',
        data_inicio: '',
        data_fim: '',
        vencidas: false
      },
      headers: [
        { text: 'Paciente', value: 'paciente', sortable: true },
        { text: 'Descrição', value: 'descricao', sortable: true },
        { text: 'Valor', value: 'valor_final', sortable: true },
        { text: 'Vencimento', value: 'data_vencimento', sortable: true },
        { text: 'Status', value: 'status', sortable: true },
        { text: 'Parcelas', value: 'parcelas', sortable: false },
        { text: 'Ações', value: 'acoes', sortable: false }
      ]
    };
  },
  async mounted() {
    await this.carregarPacientes();
    await this.carregarFaturas();
  },
  methods: {
    async carregarPacientes() {
      this.pacientes = await getListaSimplesPacientes();
    },
    
    async carregarFaturas() {
      this.isLoading = true;
      this.faturas = await getFaturas(this.filtros);
      this.isLoading = false;
    },
    
    async aplicarFiltros() {
      await this.carregarFaturas();
    },
    
    limparFiltros() {
      this.filtros = {
        paciente_id: '',
        status: '',
        data_inicio: '',
        data_fim: '',
        vencidas: false
      };
      this.carregarFaturas();
    },
    
    editarFatura(fatura) {
      this.$emit('editar-fatura', fatura);
    },
    
    async marcarComoPago(fatura) {
      const result = await cSwal.cConfirm(
        `Marcar fatura "${fatura.descricao}" como paga?`,
        'Esta ação não pode ser desfeita.'
      );
      
      if (result.isConfirmed) {
        cSwal.loading('Marcando como pago...');
        const sucesso = await marcarComoPago(fatura.id);
        cSwal.loaded();
        
        if (sucesso) {
          cSwal.cSuccess('Fatura marcada como paga!');
          await this.carregarFaturas();
        } else {
          cSwal.cError('Erro ao marcar fatura como paga.');
        }
      }
    },
    
    async excluirFatura(fatura) {
      const result = await cSwal.cConfirm(
        `Excluir fatura "${fatura.descricao}"?`,
        'Esta ação não pode ser desfeita.'
      );
      
      if (result.isConfirmed) {
        cSwal.loading('Excluindo fatura...');
        const sucesso = await excluirFatura(fatura.id);
        cSwal.loaded();
        
        if (sucesso) {
          cSwal.cSuccess('Fatura excluída com sucesso!');
          await this.carregarFaturas();
        } else {
          cSwal.cError('Erro ao excluir fatura.');
        }
      }
    },
    
    truncateText(text, maxLength) {
      if (!text) return '';
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    },
    
    // Métodos do service
    formatarValor,
    formatarData,
    getStatusClass,
    getStatusText
  }
};
</script>

<style scoped>
.faturas-container {
  padding: 1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state-message {
  color: #6c757d;
}

.icon-wrapper {
  margin-bottom: 1rem;
}

.empty-state-icon {
  font-size: 3rem;
  color: #dee2e6;
}

.valor-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.fatura-row:hover {
  background-color: #f8f9fa;
}

.table-header-item {
  font-weight: 600;
  color: #495057;
}
</style>
