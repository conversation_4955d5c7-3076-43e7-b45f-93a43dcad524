<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class ReorganizeFinanceiroReceberTable extends Migration
{
    public function up()
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Remover colunas antigas que não serão mais usadas
            $table->dropColumn([
                'caixa_id',
                'conta_id',
                'pagador_nome',
                'pagador_tipo',
                'fornecedor_id',
                'referencia',
                'notas',
                'descontos',
                'acrescimos',
                'parcela',
                'lancado_por'
            ]);
        });

        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Reorganizar e adicionar novas colunas de forma organizada
            
            // Identificação e relacionamentos
            $table->unsignedBigInteger('dentista_id')->nullable()->after('paciente_id');
            $table->foreign('dentista_id')->references('id')->on('dentistas');
            
            // Referência e descrição
            $table->string('referencia', 100)->nullable()->after('contrato_codigo');
            $table->text('observacoes')->nullable()->after('descricao');
            
            // Valores e cálculos financeiros
            $table->decimal('percentual_desconto', 5, 2)->default(0)->after('valor_nominal');
            $table->decimal('valor_desconto', 10, 2)->default(0)->after('percentual_desconto');
            $table->decimal('percentual_acrescimo', 5, 2)->default(0)->after('valor_desconto');
            $table->decimal('valor_acrescimo', 10, 2)->default(0)->after('percentual_acrescimo');
            
            // Parcelamento inteligente
            $table->unsignedInteger('parcela_numero')->default(1)->after('meio_pagamento');
            $table->unsignedBigInteger('fatura_principal_id')->nullable()->after('parcela_numero');
            $table->foreign('fatura_principal_id')->references('id')->on('financeiro_receber');
            
            // Auditoria
            $table->unsignedBigInteger('lancado_por')->nullable()->after('status');
            $table->foreign('lancado_por')->references('id')->on('users');
            
            // Índices para performance
            $table->index(['clinica_id', 'paciente_id']);
            $table->index(['status', 'data_vencimento']);
            $table->index(['fatura_principal_id']);
        });

        // Atualizar valores padrão para registros existentes
        DB::table('financeiro_receber')->update([
            'percentual_desconto' => 0,
            'valor_desconto' => 0,
            'percentual_acrescimo' => 0,
            'valor_acrescimo' => 0,
            'parcela_numero' => 1,
            'status' => 'pendente'
        ]);

        // Recalcular valor_final para registros existentes
        DB::statement('
            UPDATE financeiro_receber 
            SET valor_final = valor_nominal - valor_desconto + valor_acrescimo
            WHERE valor_final IS NULL OR valor_final = 0
        ');
    }

    public function down()
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Remover foreign keys
            $table->dropForeign(['dentista_id']);
            $table->dropForeign(['fatura_principal_id']);
            $table->dropForeign(['lancado_por']);
            
            // Remover índices
            $table->dropIndex(['clinica_id', 'paciente_id']);
            $table->dropIndex(['status', 'data_vencimento']);
            $table->dropIndex(['fatura_principal_id']);
            
            // Remover colunas adicionadas
            $table->dropColumn([
                'dentista_id',
                'observacoes',
                'percentual_desconto',
                'valor_desconto',
                'percentual_acrescimo',
                'valor_acrescimo',
                'parcela_numero',
                'fatura_principal_id'
            ]);
        });

        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Restaurar colunas antigas
            $table->integer('caixa_id')->unsigned()->nullable();
            $table->integer('conta_id')->unsigned()->nullable();
            $table->string('pagador_nome')->nullable();
            $table->string('pagador_tipo')->nullable();
            $table->integer('fornecedor_id')->unsigned()->nullable();
            $table->tinyText('referencia')->nullable();
            $table->string('notas')->nullable();
            $table->decimal('descontos', 10, 2)->nullable();
            $table->decimal('acrescimos', 10, 2)->nullable();
            $table->integer('parcela')->unsigned()->nullable();
            $table->integer('lancado_por')->unsigned()->default(0)->nullable();
        });
    }
}
