<template>
  <div class="main-page-content">
    <div class="py-4 container-fluid">
      <!-- Header com estatísticas -->
      <div class="row mb-4">
        <div class="col-lg-12 position-relative z-index-2">
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <mini-statistics-card
                :title="{ text: 'Faturas Vencidas', value: formatarValor(estatisticas.total_vencido) }"
                :detail="`${estatisticas.quantidade_vencida} faturas`"
                :icon="{
                  name: 'exclamation-triangle',
                  color: 'text-white',
                  background: 'danger',
                  type: 'fas'
                }"
              />
            </div>
            <div class="col-md-3 col-sm-6 mt-lg-0 mt-4">
              <mini-statistics-card
                :title="{ text: 'Faturas Pendentes', value: formatarValor(estatisticas.total_pendente) }"
                :detail="`${estatisticas.quantidade_pendente} faturas`"
                :icon="{
                  name: 'clock',
                  color: 'text-white',
                  background: 'warning',
                  type: 'fas',
                }"
              />
            </div>
            <div class="col-md-3 col-sm-6 mt-lg-0 mt-4">
              <mini-statistics-card
                :title="{ text: 'Faturas Pagas', value: formatarValor(estatisticas.total_pago) }"
                :detail="`${estatisticas.quantidade_paga} faturas`"
                :icon="{
                  name: 'check-circle',
                  color: 'text-white',
                  background: 'success',
                  type: 'fas',
                }"
              />
            </div>
            <div class="col-md-3 col-sm-6 mt-lg-0 mt-4">
              <mini-statistics-card
                :title="{ text: 'Total Geral', value: formatarValor(estatisticas.total_geral) }"
                detail="Todas as faturas"
                :icon="{
                  name: 'file-invoice-dollar',
                  color: 'text-white',
                  background: 'info',
                  type: 'fas',
                }"
              />
            </div>
          </div>
          <div class="row mt-4">
            <div class="col-lg-4 col-md-6 mt-4">
              <chart-holder-card title="Consultas pagas"
                subtitle="<span class='font-weight-bolder'>12</span> esta semana" update="atualizado há 5 horas">
                <reports-bar-chart :chart="{
                  labels: ['S', 'T', 'Q', 'Q', 'S', 'S', 'D'],
                  datasets: {
                    label: 'Consultas',
                    data: [50, 20, 10, 22, 50, 10, 40],
                  },
                }" />
              </chart-holder-card>
            </div>
            <div class="col-lg-4 col-md-6 mt-4">
              <chart-holder-card title="Pagamentos recebidos (histórico)"
                subtitle="<span class='font-weight-bolder'>+15%</span> em relação ao mês anterior"
                update="atualizado há 4 minutos" color="success">
                <reports-line-chart :chart="{
                  labels: [
                    'Abr',
                    'Mai',
                    'Jun',
                    'Jul',
                    'Ago',
                    'Set',
                    'Out',
                    'Nov',
                    'Dez',
                  ],
                  datasets: {
                    label: 'R$',
                    data: [50, 40, 300, 320, 500, 350, 200, 230, 500],
                  },
                }" />
              </chart-holder-card>
            </div>
            <div class="col-lg-4 mt-4">
              <chart-holder-card title="Pagamentos efetuados (histórico)"
                subtitle="<span class='font-weight-bolder'>R$ 690,00</span> este mês" update="atualizado agora"
                color="danger">
                <reports-line-chart id="tasks-chart" :chart="{
                  labels: [
                    'Abr',
                    'Mai',
                    'Jun',
                    'Jul',
                    'Ago',
                    'Set',
                    'Out',
                    'Nov',
                    'Dez',
                  ],
                  datasets: {
                    label: 'R$',
                    data: [50, 40, 300, 220, 500, 250, 400, 230, 500],
                  },
                }" />
              </chart-holder-card>
            </div>
          </div>
        </div>
      </div>

      <!-- Seção principal de gestão de faturas -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <font-awesome-icon :icon="['fas', 'file-invoice-dollar']" class="me-2" />
                Gestão de Faturas
              </h5>
              <button
                @click="abrirModalNovaFatura"
                class="btn btn-primary btn-sm"
              >
                <font-awesome-icon :icon="['fas', 'plus']" class="me-1" />
                Nova Fatura
              </button>
            </div>
            <div class="card-body">
              <faturas-table
                ref="faturasTable"
                @editar-fatura="editarFatura"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Modal de Fatura -->
      <modal-fatura
        ref="modalFatura"
        @fatura-salva="onFaturaSalva"
      />
    </div>
  </div>
</template>
<script>
import ChartHolderCard from "./components/ChartHolderCard.vue";
import ReportsBarChart from "@/examples/Charts/ReportsBarChart.vue";
import ReportsLineChart from "@/examples/Charts/ReportsLineChart.vue";
import MiniStatisticsCard from "./components/MiniStatisticsCard.vue";
import FaturasTable from "./components/FaturasTable.vue";
import ModalFatura from "@/components/ModalFatura.vue";
import { getEstatisticas, formatarValor } from '@/services/faturaService';
import { openModal } from '@/utils/modalHelper.js';

export default {
  name: "financeiro-dashboard",
  data() {
    return {
      estatisticas: {
        total_pendente: 0,
        total_vencido: 0,
        total_pago: 0,
        total_geral: 0,
        quantidade_pendente: 0,
        quantidade_vencida: 0,
        quantidade_paga: 0,
      }
    };
  },
  components: {
    ChartHolderCard,
    ReportsBarChart,
    ReportsLineChart,
    MiniStatisticsCard,
    FaturasTable,
    ModalFatura,
  },
  async mounted() {
    await this.carregarEstatisticas();
  },
  methods: {
    async carregarEstatisticas() {
      const stats = await getEstatisticas();
      if (stats) {
        this.estatisticas = stats;
      }
    },

    abrirModalNovaFatura() {
      this.$refs.modalFatura.abrirModal();
      openModal('modalFatura');
    },

    editarFatura(fatura) {
      this.$refs.modalFatura.abrirModal(fatura);
      openModal('modalFatura');
    },

    async onFaturaSalva() {
      // Recarregar estatísticas e tabela
      await this.carregarEstatisticas();
      if (this.$refs.faturasTable) {
        await this.$refs.faturasTable.carregarFaturas();
      }
    },

    // Métodos do service
    formatarValor
  }
};
</script>
