<template>
  <div class="main-page-content">
    <div class="py-4 container-fluid">
      <!-- Header com estatísticas -->
      <div class="row mb-4">
        <div class="col-lg-12 position-relative z-index-2">
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <mini-statistics-card
                :title="{ text: 'Faturas Vencidas', value: formatarValor(estatisticas.total_vencido) }"
                :detail="`${estatisticas.quantidade_vencida} faturas`"
                :icon="{
                  name: 'exclamation-triangle',
                  color: 'text-white',
                  background: 'danger',
                  type: 'fas'
                }"
              />
            </div>
            <div class="col-md-3 col-sm-6 mt-lg-0 mt-4">
              <mini-statistics-card
                :title="{ text: 'Faturas Pendentes', value: formatarValor(estatisticas.total_pendente) }"
                :detail="`${estatisticas.quantidade_pendente} faturas`"
                :icon="{
                  name: 'clock',
                  color: 'text-white',
                  background: 'warning',
                  type: 'fas',
                }"
              />
            </div>
            <div class="col-md-3 col-sm-6 mt-lg-0 mt-4">
              <mini-statistics-card
                :title="{ text: 'Faturas Pagas', value: formatarValor(estatisticas.total_pago) }"
                :detail="`${estatisticas.quantidade_paga} faturas`"
                :icon="{
                  name: 'check-circle',
                  color: 'text-white',
                  background: 'success',
                  type: 'fas',
                }"
              />
            </div>
            <div class="col-md-3 col-sm-6 mt-lg-0 mt-4">
              <mini-statistics-card
                :title="{ text: 'Total Geral', value: formatarValor(estatisticas.total_geral) }"
                detail="Todas as faturas"
                :icon="{
                  name: 'file-invoice-dollar',
                  color: 'text-white',
                  background: 'info',
                  type: 'fas',
                }"
              />
            </div>
          </div>
          <div class="row mt-4">
            <div class="col-lg-4 col-md-6 mt-4">
              <chart-holder-card title="Consultas pagas"
                subtitle="<span class='font-weight-bolder'>12</span> esta semana" update="atualizado há 5 horas">
                <reports-bar-chart :chart="{
                  labels: ['S', 'T', 'Q', 'Q', 'S', 'S', 'D'],
                  datasets: {
                    label: 'Consultas',
                    data: [50, 20, 10, 22, 50, 10, 40],
                  },
                }" />
              </chart-holder-card>
            </div>
            <div class="col-lg-4 col-md-6 mt-4">
              <chart-holder-card title="Pagamentos recebidos (histórico)"
                subtitle="<span class='font-weight-bolder'>+15%</span> em relação ao mês anterior"
                update="atualizado há 4 minutos" color="success">
                <reports-line-chart :chart="{
                  labels: [
                    'Abr',
                    'Mai',
                    'Jun',
                    'Jul',
                    'Ago',
                    'Set',
                    'Out',
                    'Nov',
                    'Dez',
                  ],
                  datasets: {
                    label: 'R$',
                    data: [50, 40, 300, 320, 500, 350, 200, 230, 500],
                  },
                }" />
              </chart-holder-card>
            </div>
            <div class="col-lg-4 mt-4">
              <chart-holder-card title="Pagamentos efetuados (histórico)"
                subtitle="<span class='font-weight-bolder'>R$ 690,00</span> este mês" update="atualizado agora"
                color="danger">
                <reports-line-chart id="tasks-chart" :chart="{
                  labels: [
                    'Abr',
                    'Mai',
                    'Jun',
                    'Jul',
                    'Ago',
                    'Set',
                    'Out',
                    'Nov',
                    'Dez',
                  ],
                  datasets: {
                    label: 'R$',
                    data: [50, 40, 300, 220, 500, 250, 400, 230, 500],
                  },
                }" />
              </chart-holder-card>
            </div>
          </div>
        </div>
      </div>

      <!-- Sistema de Abas -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <!-- Navegação das Abas -->
              <ul class="nav nav-tabs card-header-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                  <button
                    class="nav-link"
                    :class="{ active: abaAtiva === 'analitico' }"
                    @click="abaAtiva = 'analitico'"
                    type="button"
                  >
                    <font-awesome-icon :icon="['fas', 'search']" class="me-2" />
                    Analítico
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button
                    class="nav-link"
                    :class="{ active: abaAtiva === 'sintetico' }"
                    @click="abaAtiva = 'sintetico'"
                    type="button"
                  >
                    <font-awesome-icon :icon="['fas', 'chart-bar']" class="me-2" />
                    Sintético
                  </button>
                </li>
              </ul>

              <!-- Botão Nova Fatura (sempre visível) -->
              <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                <button
                  @click="abrirModalNovaFatura"
                  class="btn btn-primary btn-sm"
                >
                  <font-awesome-icon :icon="['fas', 'plus']" class="me-1" />
                  Nova Fatura
                </button>
              </div>
            </div>

            <div class="card-body">
              <!-- Conteúdo da Aba Analítico -->
              <div v-show="abaAtiva === 'analitico'" class="tab-content-analitico">
                <faturas-table
                  ref="faturasTable"
                  @editar-fatura="editarFatura"
                />
              </div>

              <!-- Conteúdo da Aba Sintético -->
              <div v-show="abaAtiva === 'sintetico'" class="tab-content-sintetico">
                <div class="row">
                  <!-- Gráficos e Relatórios -->
                  <div class="col-lg-6 col-md-12 mb-4">
                    <chart-holder-card
                      title="Faturas por Status"
                      subtitle="Distribuição atual das faturas"
                      update="atualizado agora"
                    >
                      <reports-bar-chart :chart="graficoStatus" />
                    </chart-holder-card>
                  </div>

                  <div class="col-lg-6 col-md-12 mb-4">
                    <chart-holder-card
                      title="Evolução Mensal"
                      subtitle="Faturamento dos últimos meses"
                      update="atualizado agora"
                      color="success"
                    >
                      <reports-line-chart :chart="graficoEvolucao" />
                    </chart-holder-card>
                  </div>

                  <!-- Resumo Financeiro -->
                  <div class="col-12">
                    <div class="card bg-gradient-info">
                      <div class="card-body text-white">
                        <h6 class="text-white mb-3">
                          <font-awesome-icon :icon="['fas', 'calculator']" class="me-2" />
                          Resumo Financeiro
                        </h6>
                        <div class="row">
                          <div class="col-md-3 text-center">
                            <h4 class="text-white mb-1">{{ formatarValor(estatisticas.total_pendente) }}</h4>
                            <p class="mb-0 opacity-8">Pendente</p>
                          </div>
                          <div class="col-md-3 text-center">
                            <h4 class="text-white mb-1">{{ formatarValor(estatisticas.total_vencido) }}</h4>
                            <p class="mb-0 opacity-8">Vencido</p>
                          </div>
                          <div class="col-md-3 text-center">
                            <h4 class="text-white mb-1">{{ formatarValor(estatisticas.total_pago) }}</h4>
                            <p class="mb-0 opacity-8">Pago</p>
                          </div>
                          <div class="col-md-3 text-center">
                            <h4 class="text-white mb-1">{{ formatarValor(estatisticas.total_geral) }}</h4>
                            <p class="mb-0 opacity-8">Total Geral</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal de Fatura -->
      <modal-fatura
        ref="modalFatura"
        @fatura-salva="onFaturaSalva"
      />
    </div>
  </div>
</template>
<script>
import ChartHolderCard from "./components/ChartHolderCard.vue";
import ReportsBarChart from "@/examples/Charts/ReportsBarChart.vue";
import ReportsLineChart from "@/examples/Charts/ReportsLineChart.vue";
import MiniStatisticsCard from "./components/MiniStatisticsCard.vue";
import FaturasTable from "./components/FaturasTable.vue";
import { getEstatisticas, formatarValor } from '@/services/faturaService';
import { openModal } from '@/utils/modalHelper.js';

export default {
  name: "financeiro-dashboard",
  data() {
    return {
      abaAtiva: 'analitico',
      estatisticas: {
        total_pendente: 0,
        total_vencido: 0,
        total_pago: 0,
        total_geral: 0,
        quantidade_pendente: 0,
        quantidade_vencida: 0,
        quantidade_paga: 0,
      },
      graficoStatus: {
        labels: ['Pendente', 'Vencido', 'Pago', 'Cancelado'],
        datasets: {
          label: 'Faturas',
          data: [0, 0, 0, 0],
        },
      },
      graficoEvolucao: {
        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
        datasets: {
          label: 'R$',
          data: [0, 0, 0, 0, 0, 0],
        },
      }
    };
  },
  components: {
    ChartHolderCard,
    ReportsBarChart,
    ReportsLineChart,
    MiniStatisticsCard,
    FaturasTable,
  },
  async mounted() {
    await this.carregarEstatisticas();
  },
  methods: {
    async carregarEstatisticas() {
      const stats = await getEstatisticas();
      if (stats) {
        this.estatisticas = stats;

        // Atualizar gráfico de status
        this.graficoStatus.datasets.data = [
          stats.quantidade_pendente,
          stats.quantidade_vencida,
          stats.quantidade_paga,
          0 // canceladas (pode ser implementado depois)
        ];

        // Atualizar gráfico de evolução (dados simulados por enquanto)
        this.graficoEvolucao.datasets.data = [
          stats.total_pago * 0.6,
          stats.total_pago * 0.7,
          stats.total_pago * 0.8,
          stats.total_pago * 0.9,
          stats.total_pago * 0.95,
          stats.total_pago
        ];
      }
    },

    abrirModalNovaFatura() {
      this.$refs.modalFatura.abrirModal();
      openModal('modalFatura');
    },

    editarFatura(fatura) {
      this.$refs.modalFatura.abrirModal(fatura);
      openModal('modalFatura');
    },

    async onFaturaSalva() {
      // Recarregar estatísticas e tabela
      await this.carregarEstatisticas();
      if (this.$refs.faturasTable) {
        await this.$refs.faturasTable.carregarFaturas();
      }
    },

    // Métodos do service
    formatarValor
  }
};
</script>

<style scoped>
.card-header {
  position: relative;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
  color: #495057;
  background-color: #f8f9fa;
  border-radius: 0.375rem 0.375rem 0 0;
}

.nav-tabs .nav-link.active {
  color: #495057;
  background-color: #fff;
  border-bottom: 2px solid #007bff;
  font-weight: 600;
}

.tab-content-analitico,
.tab-content-sintetico {
  min-height: 400px;
}

.bg-gradient-info {
  background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
}

.opacity-8 {
  opacity: 0.8;
}
</style>
