<template>
  <div class="faturas-paciente-container">
    <!-- Filtros Simplificados -->
    <div class="row mb-3">
      <div class="col-md-3">
        <label class="form-label">Status</label>
        <select v-model="filtros.status" @change="aplicarFiltros" class="form-select form-select-sm">
          <option value="">Todos</option>
          <option value="pendente">Pendente</option>
          <option value="pago">Pago</option>
          <option value="vencido">Vencido</option>
          <option value="cancelado">Cancelado</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Apenas Vencidas</label>
        <div class="form-check mt-2">
          <input v-model="filtros.vencidas" @change="aplicarFiltros" type="checkbox" class="form-check-input" id="vencidasPaciente">
          <label class="form-check-label" for="vencidasPaciente">Sim</label>
        </div>
      </div>
      <div class="col-md-4">
        <label class="form-label">Período</label>
        <div class="d-flex gap-2">
          <input v-model="filtros.data_inicio" @change="aplicarFiltros" type="date" class="form-control form-control-sm">
          <input v-model="filtros.data_fim" @change="aplicarFiltros" type="date" class="form-control form-control-sm">
        </div>
      </div>
      <div class="col-md-2 d-flex align-items-end">
        <button @click="limparFiltros" class="btn btn-outline-secondary btn-sm">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="isLoading" class="w-100 text-center py-3">
      <div class="spinner-border text-primary" role="status"></div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!faturas || faturas.length === 0" class="empty-state">
      <div class="empty-state-message">
        <div class="icon-wrapper">
          <font-awesome-icon :icon="['fas', 'file-invoice-dollar']" class="empty-state-icon" />
        </div>
        <p>Não há faturas registradas para este paciente.</p>
      </div>
    </div>

    <!-- Lista de Faturas (Design mais compacto para paciente) -->
    <div v-else class="faturas-list">
      <div 
        v-for="fatura in faturas" 
        :key="fatura.id" 
        class="fatura-card mb-3"
        :class="getStatusClass(fatura.status)"
      >
        <div class="card">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-6">
                <h6 class="mb-1">{{ fatura.descricao }}</h6>
                <small class="text-muted">
                  <font-awesome-icon :icon="['fas', 'calendar']" class="me-1" />
                  Vencimento: {{ formatarData(fatura.data_vencimento) }}
                </small>
                <div v-if="fatura.parcelas_total > 1" class="mt-1">
                  <span class="badge bg-info">
                    {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                  </span>
                </div>
              </div>
              
              <div class="col-md-3 text-center">
                <h5 class="mb-1">{{ formatarValor(fatura.valor_final) }}</h5>
                <span class="status-badge" :class="getStatusClass(fatura.status)">
                  {{ getStatusText(fatura.status) }}
                </span>
              </div>
              
              <div class="col-md-3 text-end">
                <div class="btn-group btn-group-sm">
                  <button 
                    @click="editarFatura(fatura)" 
                    class="btn btn-outline-primary btn-sm"
                    title="Editar"
                  >
                    <font-awesome-icon :icon="['fas', 'edit']" />
                  </button>
                  
                  <button 
                    v-if="fatura.status === 'pendente' || fatura.status === 'vencido'"
                    @click="marcarComoPago(fatura)" 
                    class="btn btn-outline-success btn-sm"
                    title="Marcar como Pago"
                  >
                    <font-awesome-icon :icon="['fas', 'check']" />
                  </button>
                  
                  <button 
                    @click="excluirFatura(fatura)" 
                    class="btn btn-outline-danger btn-sm"
                    title="Excluir"
                  >
                    <font-awesome-icon :icon="['fas', 'trash']" />
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Observações (se houver) -->
            <div v-if="fatura.observacoes" class="row mt-2">
              <div class="col-12">
                <small class="text-muted">
                  <font-awesome-icon :icon="['fas', 'sticky-note']" class="me-1" />
                  {{ fatura.observacoes }}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getFaturasPaciente, excluirFatura, marcarComoPago, formatarValor, formatarData, getStatusClass, getStatusText } from '@/services/faturaService';
import cSwal from '@/utils/cSwal.js';

export default {
  name: 'FaturasPacienteTable',
  props: {
    pacienteId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      faturas: [],
      isLoading: false,
      filtros: {
        status: '',
        data_inicio: '',
        data_fim: '',
        vencidas: false
      }
    };
  },
  watch: {
    pacienteId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.carregarFaturas();
        }
      }
    }
  },
  methods: {
    async carregarFaturas() {
      if (!this.pacienteId) return;
      
      this.isLoading = true;
      this.faturas = await getFaturasPaciente(this.pacienteId);
      this.aplicarFiltrosLocais();
      this.isLoading = false;
    },
    
    aplicarFiltrosLocais() {
      let faturasFiltradas = [...this.faturas];
      
      if (this.filtros.status) {
        faturasFiltradas = faturasFiltradas.filter(f => f.status === this.filtros.status);
      }
      
      if (this.filtros.vencidas) {
        const hoje = new Date().toISOString().split('T')[0];
        faturasFiltradas = faturasFiltradas.filter(f => 
          (f.status === 'pendente' || f.status === 'vencido') && f.data_vencimento < hoje
        );
      }
      
      if (this.filtros.data_inicio && this.filtros.data_fim) {
        faturasFiltradas = faturasFiltradas.filter(f => 
          f.data_vencimento >= this.filtros.data_inicio && 
          f.data_vencimento <= this.filtros.data_fim
        );
      }
      
      this.faturas = faturasFiltradas;
    },
    
    async aplicarFiltros() {
      await this.carregarFaturas();
    },
    
    limparFiltros() {
      this.filtros = {
        status: '',
        data_inicio: '',
        data_fim: '',
        vencidas: false
      };
      this.carregarFaturas();
    },
    
    editarFatura(fatura) {
      this.$emit('editar-fatura', fatura);
    },
    
    async marcarComoPago(fatura) {
      const result = await cSwal.cConfirm(
        `Marcar fatura "${fatura.descricao}" como paga?`,
        'Esta ação não pode ser desfeita.'
      );
      
      if (result.isConfirmed) {
        cSwal.loading('Marcando como pago...');
        const sucesso = await marcarComoPago(fatura.id);
        cSwal.loaded();
        
        if (sucesso) {
          cSwal.cSuccess('Fatura marcada como paga!');
          await this.carregarFaturas();
          this.$emit('fatura-atualizada');
        } else {
          cSwal.cError('Erro ao marcar fatura como paga.');
        }
      }
    },
    
    async excluirFatura(fatura) {
      const result = await cSwal.cConfirm(
        `Excluir fatura "${fatura.descricao}"?`,
        'Esta ação não pode ser desfeita.'
      );
      
      if (result.isConfirmed) {
        cSwal.loading('Excluindo fatura...');
        const sucesso = await excluirFatura(fatura.id);
        cSwal.loaded();
        
        if (sucesso) {
          cSwal.cSuccess('Fatura excluída com sucesso!');
          await this.carregarFaturas();
          this.$emit('fatura-atualizada');
        } else {
          cSwal.cError('Erro ao excluir fatura.');
        }
      }
    },
    
    // Métodos do service
    formatarValor,
    formatarData,
    getStatusClass,
    getStatusText
  }
};
</script>

<style scoped>
.faturas-paciente-container {
  padding: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 2rem 1rem;
}

.empty-state-message {
  color: #6c757d;
}

.icon-wrapper {
  margin-bottom: 1rem;
}

.empty-state-icon {
  font-size: 2.5rem;
  color: #dee2e6;
}

.fatura-card {
  transition: all 0.2s ease;
}

.fatura-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.bg-gradient-warning .status-badge {
  background-color: #ffc107;
}

.bg-gradient-danger .status-badge {
  background-color: #dc3545;
}

.bg-gradient-success .status-badge {
  background-color: #28a745;
}

.bg-gradient-secondary .status-badge {
  background-color: #6c757d;
}
</style>
