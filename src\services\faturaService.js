import axios from '@/services/axios'

/**
 * Buscar lista de faturas com filtros
 * @param {Object} filtros - Filtros para busca (paciente_id, status, data_inicio, data_fim, vencidas)
 * @returns {Promise<Array>} - Lista de faturas
 */
export async function getFaturas(filtros = {}) {
    try {
        const params = new URLSearchParams();
        
        if (filtros.paciente_id) params.append('paciente_id', filtros.paciente_id);
        if (filtros.status) params.append('status', filtros.status);
        if (filtros.data_inicio) params.append('data_inicio', filtros.data_inicio);
        if (filtros.data_fim) params.append('data_fim', filtros.data_fim);
        if (filtros.vencidas) params.append('vencidas', filtros.vencidas);

        const response = await axios.get(`/faturas?${params.toString()}`);

        if (!response || !response.data || !response.data.data)
            return [];

        return response.data.data;
    } catch (error) {
        console.error('Erro ao buscar faturas:', error);
        return [];
    }
}

/**
 * Buscar fatura específica por ID
 * @param {number} id - ID da fatura
 * @returns {Promise<Object|null>} - Dados da fatura
 */
export async function getFatura(id) {
    try {
        const response = await axios.get(`/faturas/${id}`);

        if (!response || !response.data || !response.data.data)
            return null;

        return response.data.data;
    } catch (error) {
        console.error('Erro ao buscar fatura:', error);
        return null;
    }
}

/**
 * Criar nova fatura
 * @param {Object} dadosFatura - Dados da fatura
 * @returns {Promise<Object|false>} - Dados da fatura criada ou false
 */
export async function criarFatura(dadosFatura) {
    try {
        const response = await axios.post('/faturas', dadosFatura);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data;
    } catch (error) {
        console.error('Erro ao criar fatura:', error);
        return false;
    }
}

/**
 * Atualizar fatura existente
 * @param {number} id - ID da fatura
 * @param {Object} dadosFatura - Dados atualizados da fatura
 * @returns {Promise<Object|false>} - Dados da fatura atualizada ou false
 */
export async function atualizarFatura(id, dadosFatura) {
    try {
        const response = await axios.put(`/faturas/${id}`, dadosFatura);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data;
    } catch (error) {
        console.error('Erro ao atualizar fatura:', error);
        return false;
    }
}

/**
 * Excluir/cancelar fatura
 * @param {number} id - ID da fatura
 * @returns {Promise<boolean>} - Sucesso da operação
 */
export async function excluirFatura(id) {
    try {
        const response = await axios.delete(`/faturas/${id}`);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return true;
    } catch (error) {
        console.error('Erro ao excluir fatura:', error);
        return false;
    }
}

/**
 * Marcar fatura como paga
 * @param {number} id - ID da fatura
 * @param {Object} dadosPagamento - Dados do pagamento (data_pagamento, meio_pagamento)
 * @returns {Promise<Object|false>} - Dados da fatura atualizada ou false
 */
export async function marcarComoPago(id, dadosPagamento = {}) {
    try {
        const response = await axios.post(`/faturas/${id}/marcar-como-pago`, dadosPagamento);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data;
    } catch (error) {
        console.error('Erro ao marcar fatura como paga:', error);
        return false;
    }
}

/**
 * Buscar faturas de um paciente específico
 * @param {number} pacienteId - ID do paciente
 * @returns {Promise<Array>} - Lista de faturas do paciente
 */
export async function getFaturasPaciente(pacienteId) {
    try {
        const response = await axios.get(`/faturas/paciente/${pacienteId}`);

        if (!response || !response.data || !response.data.data)
            return [];

        return response.data.data;
    } catch (error) {
        console.error('Erro ao buscar faturas do paciente:', error);
        return [];
    }
}

/**
 * Buscar estatísticas financeiras
 * @param {Object} filtros - Filtros para estatísticas (paciente_id)
 * @returns {Promise<Object|null>} - Estatísticas financeiras
 */
export async function getEstatisticas(filtros = {}) {
    try {
        const params = new URLSearchParams();
        
        if (filtros.paciente_id) params.append('paciente_id', filtros.paciente_id);

        const response = await axios.get(`/faturas/estatisticas?${params.toString()}`);

        if (!response || !response.data || !response.data.data)
            return null;

        return response.data.data;
    } catch (error) {
        console.error('Erro ao buscar estatísticas:', error);
        return null;
    }
}

/**
 * Calcular valor final baseado em valor nominal, descontos e acréscimos
 * @param {Object} valores - Objeto com valor_nominal, percentual_desconto, valor_desconto, percentual_acrescimo, valor_acrescimo
 * @returns {number} - Valor final calculado
 */
export function calcularValorFinal(valores) {
    const valorNominal = parseFloat(valores.valor_nominal) || 0;
    const percentualDesconto = parseFloat(valores.percentual_desconto) || 0;
    const valorDesconto = parseFloat(valores.valor_desconto) || 0;
    const percentualAcrescimo = parseFloat(valores.percentual_acrescimo) || 0;
    const valorAcrescimo = parseFloat(valores.valor_acrescimo) || 0;

    // Calcular desconto por percentual se não houver valor fixo
    let descontoFinal = valorDesconto;
    if (descontoFinal === 0 && percentualDesconto > 0) {
        descontoFinal = (valorNominal * percentualDesconto) / 100;
    }

    // Calcular acréscimo por percentual se não houver valor fixo
    let acrescimoFinal = valorAcrescimo;
    if (acrescimoFinal === 0 && percentualAcrescimo > 0) {
        acrescimoFinal = (valorNominal * percentualAcrescimo) / 100;
    }

    return valorNominal - descontoFinal + acrescimoFinal;
}

/**
 * Formatar valor monetário para exibição
 * @param {number} valor - Valor a ser formatado
 * @returns {string} - Valor formatado em reais
 */
export function formatarValor(valor) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(valor || 0);
}

/**
 * Formatar data para exibição
 * @param {string} data - Data no formato ISO
 * @returns {string} - Data formatada em pt-BR
 */
export function formatarData(data) {
    if (!data) return '';
    
    return new Date(data).toLocaleDateString('pt-BR');
}

/**
 * Obter classe CSS baseada no status da fatura
 * @param {string} status - Status da fatura
 * @returns {string} - Classe CSS
 */
export function getStatusClass(status) {
    const classMap = {
        'pendente': 'bg-gradient-warning',
        'pago': 'bg-gradient-success',
        'vencido': 'bg-gradient-danger',
        'cancelado': 'bg-gradient-secondary'
    };

    return classMap[status] || 'bg-gradient-secondary';
}

/**
 * Obter texto amigável do status
 * @param {string} status - Status da fatura
 * @returns {string} - Texto do status
 */
export function getStatusText(status) {
    const textMap = {
        'pendente': 'Pendente',
        'pago': 'Pago',
        'vencido': 'Vencido',
        'cancelado': 'Cancelado'
    };

    return textMap[status] || 'Desconhecido';
}
