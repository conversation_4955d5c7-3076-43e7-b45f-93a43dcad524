<template>
  <!-- <PERSON><PERSON> Criar/Editar Fatura -->
  <div class="modal fade lumi-fade" tabindex="-1" id="modalFatura">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ isEdicao ? 'Editar Fatura' : 'Nova Fatura' }}</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalFatura"
          ></button>
        </div>
        <div class="modal-body px-4">
          <form @submit.prevent="salvarFatura">
            <div class="row g-3">
              <!-- Paciente -->
              <div class="col-12">
                <label class="form-label">
                  <font-awesome-icon :icon="['fas', 'user']" class="me-1" />
                  Paciente <span class="text-danger">*</span>
                </label>
                <select v-model="fatura.paciente_id" class="form-select" required>
                  <option value="">Selecione um paciente</option>
                  <option v-for="paciente in pacientes" :key="paciente.id" :value="paciente.id">
                    {{ paciente.nome }}
                  </option>
                </select>
              </div>

              <!-- Descrição -->
              <div class="col-12">
                <label class="form-label">
                  <font-awesome-icon :icon="['fas', 'file-text']" class="me-1" />
                  Descrição <span class="text-danger">*</span>
                </label>
                <input 
                  v-model="fatura.descricao" 
                  type="text" 
                  class="form-control" 
                  placeholder="Ex: Consulta ortodôntica, Aparelho fixo..."
                  required
                >
              </div>

              <!-- Valor Nominal e Data de Vencimento -->
              <div class="col-md-6">
                <label class="form-label">
                  <font-awesome-icon :icon="['fas', 'dollar-sign']" class="me-1" />
                  Valor Nominal <span class="text-danger">*</span>
                </label>
                <input 
                  v-model="fatura.valor_nominal" 
                  type="number" 
                  step="0.01" 
                  min="0.01"
                  class="form-control" 
                  placeholder="0,00"
                  @input="calcularValorFinal"
                  required
                >
              </div>

              <div class="col-md-6">
                <label class="form-label">
                  <font-awesome-icon :icon="['fas', 'calendar']" class="me-1" />
                  Data de Vencimento <span class="text-danger">*</span>
                </label>
                <input 
                  v-model="fatura.data_vencimento" 
                  type="date" 
                  class="form-control" 
                  required
                >
              </div>

              <!-- Parcelamento -->
              <div class="col-md-6">
                <label class="form-label">
                  <font-awesome-icon :icon="['fas', 'credit-card']" class="me-1" />
                  Número de Parcelas
                </label>
                <select v-model="fatura.parcelas_total" class="form-select">
                  <option value="1">À vista</option>
                  <option v-for="n in opcoesParcelamento" :key="n" :value="n">{{ n }}x</option>
                </select>
              </div>

              <!-- Dentista (opcional) -->
              <div class="col-md-6">
                <label class="form-label">
                  <font-awesome-icon :icon="['fas', 'user-md']" class="me-1" />
                  Ortodontista
                </label>
                <select v-model="fatura.dentista_id" class="form-select">
                  <option value="">Selecione (opcional)</option>
                  <option v-for="dentista in dentistas" :key="dentista.id" :value="dentista.id">
                    {{ dentista.nome }}
                  </option>
                </select>
              </div>

              <!-- Seção de Descontos e Acréscimos -->
              <div class="col-12">
                <hr class="my-3">
                <h6 class="mb-3">
                  <font-awesome-icon :icon="['fas', 'calculator']" class="me-1" />
                  Descontos e Acréscimos
                </h6>
              </div>

              <!-- Desconto -->
              <div class="col-md-6">
                <label class="form-label">Desconto (%)</label>
                <input 
                  v-model="fatura.percentual_desconto" 
                  type="number" 
                  step="0.01" 
                  min="0"
                  max="100"
                  class="form-control" 
                  placeholder="0,00"
                  @input="calcularValorFinal"
                >
              </div>

              <div class="col-md-6">
                <label class="form-label">Desconto (R$)</label>
                <input 
                  v-model="fatura.valor_desconto" 
                  type="number" 
                  step="0.01" 
                  min="0"
                  class="form-control" 
                  placeholder="0,00"
                  @input="calcularValorFinal"
                >
              </div>

              <!-- Acréscimo -->
              <div class="col-md-6">
                <label class="form-label">Acréscimo (%)</label>
                <input 
                  v-model="fatura.percentual_acrescimo" 
                  type="number" 
                  step="0.01" 
                  min="0"
                  class="form-control" 
                  placeholder="0,00"
                  @input="calcularValorFinal"
                >
              </div>

              <div class="col-md-6">
                <label class="form-label">Acréscimo (R$)</label>
                <input 
                  v-model="fatura.valor_acrescimo" 
                  type="number" 
                  step="0.01" 
                  min="0"
                  class="form-control" 
                  placeholder="0,00"
                  @input="calcularValorFinal"
                >
              </div>

              <!-- Valor Final Calculado -->
              <div class="col-12">
                <div class="alert alert-info d-flex align-items-center">
                  <font-awesome-icon :icon="['fas', 'info-circle']" class="me-2" />
                  <strong>Valor Final: {{ formatarValor(valorFinalCalculado) }}</strong>
                  <span v-if="fatura.parcelas_total > 1" class="ms-2">
                    ({{ fatura.parcelas_total }}x de {{ formatarValor(valorFinalCalculado / fatura.parcelas_total) }})
                  </span>
                </div>
              </div>

              <!-- Observações -->
              <div class="col-12">
                <label class="form-label">
                  <font-awesome-icon :icon="['fas', 'sticky-note']" class="me-1" />
                  Observações
                </label>
                <textarea 
                  v-model="fatura.observacoes" 
                  class="form-control" 
                  rows="3"
                  placeholder="Observações adicionais sobre a fatura..."
                ></textarea>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            Cancelar
          </button>
          <button 
            type="button" 
            class="btn btn-primary" 
            @click="salvarFatura"
            :disabled="!isFormValido || isLoading"
          >
            <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
            {{ isEdicao ? 'Atualizar' : 'Criar' }} Fatura
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { criarFatura, atualizarFatura, calcularValorFinal, formatarValor } from '@/services/faturaService';
import { getListaSimplesPacientes } from '@/services/pacientesService';
import { getDentistas } from '@/services/dentistasService';
import { closeModalWithAnimation } from '@/utils/modalHelper.js';
import cSwal from '@/utils/cSwal.js';

export default {
  name: 'ModalFatura',
  data() {
    return {
      isEdicao: false,
      isLoading: false,
      pacientes: [],
      dentistas: [],
      fatura: this.getFaturaVazia(),
      valorFinalCalculado: 0
    };
  },
  computed: {
    isFormValido() {
      return this.fatura.paciente_id &&
             this.fatura.descricao &&
             this.fatura.valor_nominal > 0 &&
             this.fatura.data_vencimento;
    },

    opcoesParcelamento() {
      // Gerar array de 2 a 60 para as opções de parcelamento
      return Array.from({ length: 59 }, (_, i) => i + 2);
    }
  },
  async mounted() {
    await this.carregarDados();
  },
  methods: {
    async carregarDados() {
      this.pacientes = await getListaSimplesPacientes();
      this.dentistas = await getDentistas();
    },

    getFaturaVazia() {
      return {
        paciente_id: '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      };
    },

    abrirModal(fatura = null) {
      this.isEdicao = !!fatura;
      
      if (fatura) {
        this.fatura = { ...fatura };
      } else {
        this.fatura = this.getFaturaVazia();
      }
      
      this.calcularValorFinal();
    },

    calcularValorFinal() {
      this.valorFinalCalculado = calcularValorFinal(this.fatura);
    },

    async salvarFatura() {
      if (!this.isFormValido) return;

      this.isLoading = true;
      
      try {
        let resultado;
        
        if (this.isEdicao) {
          resultado = await atualizarFatura(this.fatura.id, this.fatura);
        } else {
          resultado = await criarFatura(this.fatura);
        }

        if (resultado) {
          const mensagem = this.isEdicao ? 'Fatura atualizada com sucesso!' : 'Fatura criada com sucesso!';
          cSwal.cSuccess(mensagem);
          
          closeModalWithAnimation('modalFatura');
          this.$emit('fatura-salva', resultado);
          
          // Resetar formulário
          this.fatura = this.getFaturaVazia();
          this.calcularValorFinal();
        } else {
          cSwal.cError('Erro ao salvar fatura. Tente novamente.');
        }
      } catch (error) {
        console.error('Erro ao salvar fatura:', error);
        cSwal.cError('Erro ao salvar fatura. Tente novamente.');
      } finally {
        this.isLoading = false;
      }
    },

    // Métodos do service
    formatarValor
  }
};
</script>

<style scoped>
.alert-info {
  background-color: #e3f2fd;
  border-color: #bbdefb;
  color: #1976d2;
}

.form-label {
  font-weight: 600;
  color: #495057;
}

.text-danger {
  color: #dc3545 !important;
}

.modal-body {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
