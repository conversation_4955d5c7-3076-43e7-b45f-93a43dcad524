<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('modelos3d', function (Blueprint $table) {
            // Campos para agrupamento de modelos 3D
            $table->string('grupo_exame_id')->nullable()->after('tag_diagnostico');
            $table->string('grupo_nome')->nullable()->after('grupo_exame_id');
            $table->integer('ordem_no_grupo')->nullable()->after('grupo_nome');
            $table->string('tipo_modelo')->nullable()->after('ordem_no_grupo');

            // Índices para performance
            $table->index(['grupo_exame_id']);
            $table->index(['paciente_id', 'grupo_exame_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('modelos3d', function (Blueprint $table) {
            // Primeiro remove as chaves estrangeiras
            $table->dropForeign(['grupo_exame_id']);
            $table->dropForeign(['paciente_id']);
            
            // Depois remove os índices
            $table->dropIndex(['grupo_exame_id']);
            $table->dropIndex(['paciente_id', 'grupo_exame_id']);
            
            // Finalmente remove as colunas
            $table->dropColumn([
                'grupo_exame_id',
                'grupo_nome',
                'ordem_no_grupo',
                'tipo_modelo'
            ]);
        });
    }
};
